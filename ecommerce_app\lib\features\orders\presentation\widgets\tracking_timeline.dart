import 'package:flutter/material.dart';
import 'package:intl/intl.dart';
import '../../../../core/theme/app_colors.dart';
import '../../../../core/theme/app_text_styles.dart';
import '../../models/tracking_event.dart';
import '../../models/order_status.dart';

class TrackingTimeline extends StatelessWidget {
  final List<TrackingEvent> events;

  const TrackingTimeline({
    super.key,
    required this.events,
  });

  @override
  Widget build(BuildContext context) {
    if (events.isEmpty) {
      return const SizedBox.shrink();
    }

    // Sort events by timestamp (newest first)
    final sortedEvents = List<TrackingEvent>.from(events)
      ..sort((a, b) => b.timestamp.compareTo(a.timestamp));

    return Column(
      children: [
        for (int i = 0; i < sortedEvents.length; i++)
          _buildTimelineItem(
            sortedEvents[i],
            isFirst: i == 0,
            isLast: i == sortedEvents.length - 1,
          ),
      ],
    );
  }

  Widget _buildTimelineItem(
    TrackingEvent event, {
    required bool isFirst,
    required bool isLast,
  }) {
    final statusColor = _getStatusColor(event.status);
    
    return IntrinsicHeight(
      child: Row(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          // Timeline indicator
          Column(
            children: [
              // Top line (if not first)
              if (!isFirst)
                Container(
                  width: 2,
                  height: 20,
                  color: event.isCompleted 
                      ? statusColor.withOpacity(0.3)
                      : AppColors.greyLight,
                ),
              
              // Status dot
              Container(
                width: 24,
                height: 24,
                decoration: BoxDecoration(
                  color: event.isCompleted ? statusColor : AppColors.greyLight,
                  shape: BoxShape.circle,
                  border: Border.all(
                    color: event.isCompleted ? statusColor : AppColors.border,
                    width: 2,
                  ),
                ),
                child: event.isCompleted
                    ? const Icon(
                        Icons.check,
                        size: 14,
                        color: AppColors.white,
                      )
                    : Container(
                        width: 8,
                        height: 8,
                        decoration: const BoxDecoration(
                          color: AppColors.greyLight,
                          shape: BoxShape.circle,
                        ),
                      ),
              ),
              
              // Bottom line (if not last)
              if (!isLast)
                Expanded(
                  child: Container(
                    width: 2,
                    color: event.isCompleted 
                        ? statusColor.withOpacity(0.3)
                        : AppColors.greyLight,
                  ),
                ),
            ],
          ),
          
          const SizedBox(width: 16),
          
          // Event content
          Expanded(
            child: Container(
              padding: const EdgeInsets.only(bottom: 24),
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  // Event title and time
                  Row(
                    crossAxisAlignment: CrossAxisAlignment.start,
                    children: [
                      Expanded(
                        child: Text(
                          event.title,
                          style: AppTextStyles.titleSmall.copyWith(
                            fontWeight: FontWeight.w600,
                            color: event.isCompleted 
                                ? AppColors.textPrimary 
                                : AppColors.textSecondary,
                          ),
                        ),
                      ),
                      const SizedBox(width: 8),
                      Text(
                        DateFormat('MMM dd, HH:mm').format(event.timestamp),
                        style: AppTextStyles.bodySmall.copyWith(
                          color: AppColors.textSecondary,
                        ),
                      ),
                    ],
                  ),
                  
                  const SizedBox(height: 4),
                  
                  // Event description
                  Text(
                    event.description,
                    style: AppTextStyles.bodyMedium.copyWith(
                      color: event.isCompleted 
                          ? AppColors.textSecondary 
                          : AppColors.textHint,
                    ),
                  ),
                  
                  // Location (if available)
                  if (event.location != null) ...[
                    const SizedBox(height: 4),
                    Row(
                      children: [
                        Icon(
                          Icons.location_on_outlined,
                          size: 16,
                          color: event.isCompleted 
                              ? statusColor 
                              : AppColors.textHint,
                        ),
                        const SizedBox(width: 4),
                        Text(
                          event.location!,
                          style: AppTextStyles.bodySmall.copyWith(
                            color: event.isCompleted 
                                ? statusColor 
                                : AppColors.textHint,
                            fontWeight: FontWeight.w500,
                          ),
                        ),
                      ],
                    ),
                  ],
                  
                  // Status badge for current status
                  if (isFirst && event.isCompleted) ...[
                    const SizedBox(height: 8),
                    Container(
                      padding: const EdgeInsets.symmetric(
                        horizontal: 8,
                        vertical: 4,
                      ),
                      decoration: BoxDecoration(
                        color: statusColor.withOpacity(0.1),
                        borderRadius: BorderRadius.circular(12),
                        border: Border.all(
                          color: statusColor.withOpacity(0.3),
                        ),
                      ),
                      child: Row(
                        mainAxisSize: MainAxisSize.min,
                        children: [
                          Text(
                            event.status.icon,
                            style: const TextStyle(fontSize: 12),
                          ),
                          const SizedBox(width: 4),
                          Text(
                            'Current Status',
                            style: AppTextStyles.labelSmall.copyWith(
                              color: statusColor,
                              fontWeight: FontWeight.w600,
                            ),
                          ),
                        ],
                      ),
                    ),
                  ],
                ],
              ),
            ),
          ),
        ],
      ),
    );
  }

  Color _getStatusColor(OrderStatus status) {
    switch (status) {
      case OrderStatus.pending:
        return AppColors.accent;
      case OrderStatus.confirmed:
        return AppColors.secondary;
      case OrderStatus.processing:
        return AppColors.primary;
      case OrderStatus.shipped:
        return AppColors.primary;
      case OrderStatus.outForDelivery:
        return AppColors.secondary;
      case OrderStatus.delivered:
        return AppColors.success;
      case OrderStatus.cancelled:
        return AppColors.error;
      case OrderStatus.returned:
        return AppColors.warning;
    }
  }
}
