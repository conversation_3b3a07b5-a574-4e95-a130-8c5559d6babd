import 'order_status.dart';

class TrackingEvent {
  final String id;
  final OrderStatus status;
  final String title;
  final String description;
  final DateTime timestamp;
  final String? location;
  final bool isCompleted;

  const TrackingEvent({
    required this.id,
    required this.status,
    required this.title,
    required this.description,
    required this.timestamp,
    this.location,
    required this.isCompleted,
  });

  factory TrackingEvent.fromJson(Map<String, dynamic> json) {
    return TrackingEvent(
      id: json['id'] as String,
      status: OrderStatus.values.firstWhere(
        (e) => e.name == json['status'],
        orElse: () => OrderStatus.pending,
      ),
      title: json['title'] as String,
      description: json['description'] as String,
      timestamp: DateTime.parse(json['timestamp'] as String),
      location: json['location'] as String?,
      isCompleted: json['isCompleted'] as bool,
    );
  }

  Map<String, dynamic> toJson() {
    return {
      'id': id,
      'status': status.name,
      'title': title,
      'description': description,
      'timestamp': timestamp.toIso8601String(),
      'location': location,
      'isCompleted': isCompleted,
    };
  }

  TrackingEvent copyWith({
    String? id,
    OrderStatus? status,
    String? title,
    String? description,
    DateTime? timestamp,
    String? location,
    bool? isCompleted,
  }) {
    return TrackingEvent(
      id: id ?? this.id,
      status: status ?? this.status,
      title: title ?? this.title,
      description: description ?? this.description,
      timestamp: timestamp ?? this.timestamp,
      location: location ?? this.location,
      isCompleted: isCompleted ?? this.isCompleted,
    );
  }

  @override
  bool operator ==(Object other) {
    if (identical(this, other)) return true;
    return other is TrackingEvent && other.id == id;
  }

  @override
  int get hashCode => id.hashCode;

  @override
  String toString() {
    return 'TrackingEvent(id: $id, status: $status, title: $title, timestamp: $timestamp)';
  }
}
