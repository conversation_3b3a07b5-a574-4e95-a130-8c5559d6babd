enum OrderStatus {
  pending,
  confirmed,
  processing,
  shipped,
  outForDelivery,
  delivered,
  cancelled,
  returned,
}

extension OrderStatusExtension on OrderStatus {
  String get displayName {
    switch (this) {
      case OrderStatus.pending:
        return 'Order Pending';
      case OrderStatus.confirmed:
        return 'Order Confirmed';
      case OrderStatus.processing:
        return 'Processing';
      case OrderStatus.shipped:
        return 'Shipped';
      case OrderStatus.outForDelivery:
        return 'Out for Delivery';
      case OrderStatus.delivered:
        return 'Delivered';
      case OrderStatus.cancelled:
        return 'Cancelled';
      case OrderStatus.returned:
        return 'Returned';
    }
  }

  String get description {
    switch (this) {
      case OrderStatus.pending:
        return 'Your order has been received and is awaiting confirmation';
      case OrderStatus.confirmed:
        return 'Your order has been confirmed and is being prepared';
      case OrderStatus.processing:
        return 'Your order is being processed and packaged';
      case OrderStatus.shipped:
        return 'Your order has been shipped and is on its way';
      case OrderStatus.outForDelivery:
        return 'Your order is out for delivery and will arrive soon';
      case OrderStatus.delivered:
        return 'Your order has been successfully delivered';
      case OrderStatus.cancelled:
        return 'Your order has been cancelled';
      case OrderStatus.returned:
        return 'Your order has been returned';
    }
  }

  String get icon {
    switch (this) {
      case OrderStatus.pending:
        return '⏳';
      case OrderStatus.confirmed:
        return '✅';
      case OrderStatus.processing:
        return '📦';
      case OrderStatus.shipped:
        return '🚚';
      case OrderStatus.outForDelivery:
        return '🚛';
      case OrderStatus.delivered:
        return '🎉';
      case OrderStatus.cancelled:
        return '❌';
      case OrderStatus.returned:
        return '↩️';
    }
  }

  bool get isActive {
    switch (this) {
      case OrderStatus.pending:
      case OrderStatus.confirmed:
      case OrderStatus.processing:
      case OrderStatus.shipped:
      case OrderStatus.outForDelivery:
        return true;
      case OrderStatus.delivered:
      case OrderStatus.cancelled:
      case OrderStatus.returned:
        return false;
    }
  }

  bool get isCompleted {
    return this == OrderStatus.delivered;
  }

  bool get isCancelled {
    return this == OrderStatus.cancelled || this == OrderStatus.returned;
  }
}
