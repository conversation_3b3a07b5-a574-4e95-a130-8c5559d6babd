import 'package:flutter/material.dart';
import '../../../core/theme/app_colors.dart';
import '../../../core/theme/app_text_styles.dart';
import '../../../shared/widgets/custom_text_field.dart';
import '../../../shared/widgets/custom_button.dart';
import '../../../shared/models/order.dart';
import '../../../shared/models/product.dart';
import '../models/tracked_order.dart';
import '../models/order_status.dart';
import '../models/tracking_event.dart';
import 'widgets/order_card.dart';
import 'widgets/order_details_sheet.dart';

class TrackOrdersScreen extends StatefulWidget {
  const TrackOrdersScreen({super.key});

  @override
  State<TrackOrdersScreen> createState() => _TrackOrdersScreenState();
}

class _TrackOrdersScreenState extends State<TrackOrdersScreen>
    with TickerProviderStateMixin {
  final TextEditingController _searchController = TextEditingController();
  late TabController _tabController;
  List<TrackedOrder> _orders = [];
  List<TrackedOrder> _filteredOrders = [];
  bool _isLoading = false;
  String _selectedFilter = 'all';

  @override
  void initState() {
    super.initState();
    _tabController = TabController(length: 4, vsync: this);
    _loadOrders();
  }

  @override
  void dispose() {
    _searchController.dispose();
    _tabController.dispose();
    super.dispose();
  }

  void _loadOrders() {
    setState(() {
      _isLoading = true;
    });

    // Simulate API call
    Future.delayed(const Duration(milliseconds: 800), () {
      setState(() {
        _orders = _getMockOrders();
        _filteredOrders = _orders;
        _isLoading = false;
      });
    });
  }

  void _filterOrders(String filter) {
    setState(() {
      _selectedFilter = filter;
      switch (filter) {
        case 'active':
          _filteredOrders = _orders.where((order) => order.currentStatus.isActive).toList();
          break;
        case 'delivered':
          _filteredOrders = _orders.where((order) => order.currentStatus.isCompleted).toList();
          break;
        case 'cancelled':
          _filteredOrders = _orders.where((order) => order.currentStatus.isCancelled).toList();
          break;
        default:
          _filteredOrders = _orders;
      }
    });
  }

  void _searchOrders(String query) {
    setState(() {
      if (query.isEmpty) {
        _filteredOrders = _orders;
      } else {
        _filteredOrders = _orders.where((order) {
          return order.orderNumber.toLowerCase().contains(query.toLowerCase()) ||
              order.items.any((item) => 
                  item.product.name.toLowerCase().contains(query.toLowerCase()));
        }).toList();
      }
    });
  }

  void _showOrderDetails(TrackedOrder order) {
    showModalBottomSheet(
      context: context,
      isScrollControlled: true,
      backgroundColor: Colors.transparent,
      builder: (context) => OrderDetailsSheet(order: order),
    );
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      backgroundColor: AppColors.background,
      appBar: AppBar(
        title: const Text('Track Orders'),
        elevation: 0,
        backgroundColor: AppColors.white,
        foregroundColor: AppColors.textPrimary,
        actions: [
          IconButton(
            onPressed: _loadOrders,
            icon: const Icon(Icons.refresh),
          ),
        ],
      ),
      body: Column(
        children: [
          // Search and Filter Section
          Container(
            color: AppColors.white,
            padding: const EdgeInsets.all(16),
            child: Column(
              children: [
                // Search Bar
                CustomTextField(
                  controller: _searchController,
                  hintText: 'Search by order number or product...',
                  prefixIcon: Icons.search,
                  onChanged: _searchOrders,
                ),
                const SizedBox(height: 16),
                
                // Filter Tabs
                Container(
                  decoration: BoxDecoration(
                    color: AppColors.greyLight,
                    borderRadius: BorderRadius.circular(12),
                  ),
                  child: TabBar(
                    controller: _tabController,
                    onTap: (index) {
                      final filters = ['all', 'active', 'delivered', 'cancelled'];
                      _filterOrders(filters[index]);
                    },
                    indicator: BoxDecoration(
                      color: AppColors.primary,
                      borderRadius: BorderRadius.circular(12),
                    ),
                    labelColor: AppColors.white,
                    unselectedLabelColor: AppColors.textSecondary,
                    labelStyle: AppTextStyles.labelMedium.copyWith(
                      fontWeight: FontWeight.w600,
                    ),
                    tabs: const [
                      Tab(text: 'All'),
                      Tab(text: 'Active'),
                      Tab(text: 'Delivered'),
                      Tab(text: 'Cancelled'),
                    ],
                  ),
                ),
              ],
            ),
          ),
          
          // Orders List
          Expanded(
            child: _isLoading
                ? _buildLoadingState()
                : _filteredOrders.isEmpty
                    ? _buildEmptyState()
                    : _buildOrdersList(),
          ),
        ],
      ),
    );
  }

  Widget _buildLoadingState() {
    return const Center(
      child: Column(
        mainAxisAlignment: MainAxisAlignment.center,
        children: [
          CircularProgressIndicator(
            valueColor: AlwaysStoppedAnimation<Color>(AppColors.primary),
          ),
          SizedBox(height: 16),
          Text(
            'Loading your orders...',
            style: AppTextStyles.bodyMedium,
          ),
        ],
      ),
    );
  }

  Widget _buildEmptyState() {
    return Center(
      child: Column(
        mainAxisAlignment: MainAxisAlignment.center,
        children: [
          Container(
            padding: const EdgeInsets.all(24),
            decoration: BoxDecoration(
              color: AppColors.primary.withOpacity(0.1),
              shape: BoxShape.circle,
            ),
            child: const Icon(
              Icons.shopping_bag_outlined,
              size: 64,
              color: AppColors.primary,
            ),
          ),
          const SizedBox(height: 24),
          Text(
            'No Orders Found',
            style: AppTextStyles.headlineSmall.copyWith(
              fontWeight: FontWeight.bold,
            ),
          ),
          const SizedBox(height: 8),
          Text(
            'You haven\'t placed any orders yet.\nStart shopping to see your orders here!',
            textAlign: TextAlign.center,
            style: AppTextStyles.bodyMedium.copyWith(
              color: AppColors.textSecondary,
            ),
          ),
          const SizedBox(height: 24),
          PrimaryButton(
            text: 'Start Shopping',
            onPressed: () => Navigator.of(context).pop(),
          ),
        ],
      ),
    );
  }

  Widget _buildOrdersList() {
    return ListView.builder(
      padding: const EdgeInsets.all(16),
      itemCount: _filteredOrders.length,
      itemBuilder: (context, index) {
        final order = _filteredOrders[index];
        return Padding(
          padding: const EdgeInsets.only(bottom: 16),
          child: OrderCard(
            order: order,
            onTap: () => _showOrderDetails(order),
          ),
        );
      },
    );
  }

  List<TrackedOrder> _getMockOrders() {
    final now = DateTime.now();
    return [
      // Active order - Shipped
      TrackedOrder(
        id: '1',
        orderNumber: 'ORD-2024-001',
        orderDate: now.subtract(const Duration(days: 3)),
        currentStatus: OrderStatus.shipped,
        totalAmount: 299.99,
        items: _getMockOrderItems(),
        shippingAddress: _getMockShippingAddress(),
        paymentMethod: _getMockPaymentMethod(),
        trackingEvents: _getMockTrackingEvents(OrderStatus.shipped),
        estimatedDelivery: now.add(const Duration(days: 2)),
        trackingNumber: 'TRK123456789',
        courierName: 'FastDelivery Express',
        courierPhone: '******-FAST-DEL',
      ),
      
      // Active order - Processing
      TrackedOrder(
        id: '2',
        orderNumber: 'ORD-2024-002',
        orderDate: now.subtract(const Duration(days: 1)),
        currentStatus: OrderStatus.processing,
        totalAmount: 149.99,
        items: _getMockOrderItems().take(2).toList(),
        shippingAddress: _getMockShippingAddress(),
        paymentMethod: _getMockPaymentMethod(),
        trackingEvents: _getMockTrackingEvents(OrderStatus.processing),
        estimatedDelivery: now.add(const Duration(days: 4)),
      ),
      
      // Delivered order
      TrackedOrder(
        id: '3',
        orderNumber: 'ORD-2024-003',
        orderDate: now.subtract(const Duration(days: 7)),
        currentStatus: OrderStatus.delivered,
        totalAmount: 89.99,
        items: _getMockOrderItems().take(1).toList(),
        shippingAddress: _getMockShippingAddress(),
        paymentMethod: _getMockPaymentMethod(),
        trackingEvents: _getMockTrackingEvents(OrderStatus.delivered),
        trackingNumber: 'TRK987654321',
        courierName: 'QuickShip Logistics',
      ),
    ];
  }

  List<OrderItem> _getMockOrderItems() {
    return [
      OrderItem(
        id: '1',
        product: Product(
          id: '1',
          name: 'Wireless Bluetooth Headphones',
          description: 'Premium quality wireless headphones',
          price: 99.99,
          imageUrl: 'https://images.unsplash.com/photo-1505740420928-5e560c06d30e?w=400',
          category: 'Electronics',
          rating: 4.5,
          reviewCount: 128,
          isInStock: true,
          colors: ['Black', 'White'],
          sizes: [],
        ),
        quantity: 1,
        price: 99.99,
      ),
      OrderItem(
        id: '2',
        product: Product(
          id: '2',
          name: 'Smart Fitness Watch',
          description: 'Advanced fitness tracking watch',
          price: 199.99,
          imageUrl: 'https://images.unsplash.com/photo-1523275335684-37898b6baf30?w=400',
          category: 'Electronics',
          rating: 4.7,
          reviewCount: 89,
          isInStock: true,
          colors: ['Black', 'Silver'],
          sizes: [],
        ),
        quantity: 1,
        price: 199.99,
      ),
    ];
  }

  ShippingAddress _getMockShippingAddress() {
    return const ShippingAddress(
      id: '1',
      fullName: 'John Doe',
      phoneNumber: '******-0123',
      addressLine1: '123 Main Street',
      addressLine2: 'Apt 4B',
      city: 'New York',
      state: 'NY',
      zipCode: '10001',
      country: 'United States',
      isDefault: true,
    );
  }

  PaymentMethod _getMockPaymentMethod() {
    return const PaymentMethod(
      id: '1',
      type: PaymentMethodType.creditCard,
      cardNumber: '**** **** **** 1234',
      cardHolderName: 'John Doe',
      expiryDate: '12/25',
      isDefault: true,
    );
  }

  List<TrackingEvent> _getMockTrackingEvents(OrderStatus currentStatus) {
    final now = DateTime.now();
    final events = <TrackingEvent>[];

    // Always add order placed event
    events.add(TrackingEvent(
      id: '1',
      status: OrderStatus.pending,
      title: 'Order Placed',
      description: 'Your order has been successfully placed',
      timestamp: now.subtract(const Duration(days: 3)),
      isCompleted: true,
    ));

    if (currentStatus.index >= OrderStatus.confirmed.index) {
      events.add(TrackingEvent(
        id: '2',
        status: OrderStatus.confirmed,
        title: 'Order Confirmed',
        description: 'Your order has been confirmed and payment processed',
        timestamp: now.subtract(const Duration(days: 3, hours: 2)),
        isCompleted: true,
      ));
    }

    if (currentStatus.index >= OrderStatus.processing.index) {
      events.add(TrackingEvent(
        id: '3',
        status: OrderStatus.processing,
        title: 'Processing Order',
        description: 'Your order is being prepared for shipment',
        timestamp: now.subtract(const Duration(days: 2)),
        location: 'Fulfillment Center - NY',
        isCompleted: true,
      ));
    }

    if (currentStatus.index >= OrderStatus.shipped.index) {
      events.add(TrackingEvent(
        id: '4',
        status: OrderStatus.shipped,
        title: 'Order Shipped',
        description: 'Your order has been shipped and is on its way',
        timestamp: now.subtract(const Duration(days: 1)),
        location: 'Distribution Center - NY',
        isCompleted: true,
      ));
    }

    if (currentStatus.index >= OrderStatus.outForDelivery.index) {
      events.add(TrackingEvent(
        id: '5',
        status: OrderStatus.outForDelivery,
        title: 'Out for Delivery',
        description: 'Your order is out for delivery',
        timestamp: now.subtract(const Duration(hours: 4)),
        location: 'Local Delivery Hub',
        isCompleted: true,
      ));
    }

    if (currentStatus == OrderStatus.delivered) {
      events.add(TrackingEvent(
        id: '6',
        status: OrderStatus.delivered,
        title: 'Delivered',
        description: 'Your order has been successfully delivered',
        timestamp: now.subtract(const Duration(hours: 2)),
        location: 'Front Door',
        isCompleted: true,
      ));
    }

    return events;
  }
}
