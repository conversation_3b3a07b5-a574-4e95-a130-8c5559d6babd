{"net": {"http_server_properties": {"broken_alternative_services": [{"anonymization": ["GAAAABIAAABodHRwczovL2dvb2dsZS5jb20AAA==", false, 0], "broken_count": 1, "host": "accounts.google.com", "port": 443, "protocol_str": "quic"}, {"anonymization": ["FAAAABAAAABodHRwOi8vbG9jYWxob3N0", false, 0], "broken_count": 2, "host": "www.gstatic.com", "port": 443, "protocol_str": "quic"}, {"anonymization": ["MAAAACsAAABodHRwczovL29wdGltaXphdGlvbmd1aWRlLXBhLmdvb2dsZWFwaXMuY29tAA==", false, 0], "broken_count": 18, "broken_until": "**********", "host": "optimizationguide-pa.googleapis.com", "port": 443, "protocol_str": "quic"}, {"anonymization": ["FAAAABAAAABodHRwOi8vbG9jYWxob3N0", false, 0], "broken_count": 1, "host": "content-autofill.googleapis.com", "port": 443, "protocol_str": "quic"}, {"anonymization": ["MAAAACsAAABodHRwczovL29wdGltaXphdGlvbmd1aWRlLXBhLmdvb2dsZWFwaXMuY29tAA==", false, 0], "broken_count": 2, "host": "beacons.gcp.gvt2.com", "port": 443, "protocol_str": "quic"}, {"anonymization": ["FAAAABAAAABodHRwOi8vbG9jYWxob3N0", false, 0], "broken_count": 14, "broken_until": "**********", "host": "fonts.gstatic.com", "port": 443, "protocol_str": "quic"}, {"anonymization": ["GAAAABIAAABodHRwczovL2dvb2dsZS5jb20AAA==", false, 0], "broken_count": 6, "broken_until": "**********", "host": "android.clients.google.com", "port": 443, "protocol_str": "quic"}], "servers": [{"anonymization": ["MAAAACsAAABodHRwczovL29wdGltaXphdGlvbmd1aWRlLXBhLmdvb2dsZWFwaXMuY29tAA==", false, 0], "server": "https://beacons.gcp.gvt2.com", "supports_spdy": true}, {"alternative_service": [{"advertised_alpns": ["h3"], "expiration": "*****************", "port": 443, "protocol_str": "quic"}], "anonymization": ["GAAAABIAAABodHRwczovL2dvb2dsZS5jb20AAA==", false, 0], "server": "https://accounts.google.com", "supports_spdy": true}, {"alternative_service": [{"advertised_alpns": ["h3"], "expiration": "*****************", "port": 443, "protocol_str": "quic"}], "anonymization": ["FAAAABAAAABodHRwOi8vbG9jYWxob3N0", false, 0], "server": "https://www.gstatic.com", "supports_spdy": true}, {"alternative_service": [{"advertised_alpns": ["h3"], "expiration": "*****************", "port": 443, "protocol_str": "quic"}], "anonymization": ["FAAAABAAAABodHRwOi8vbG9jYWxob3N0", false, 0], "server": "https://content-autofill.googleapis.com", "supports_spdy": true}, {"alternative_service": [{"advertised_alpns": ["h3"], "expiration": "*****************", "port": 443, "protocol_str": "quic"}], "anonymization": ["MAAAACsAAABodHRwczovL29wdGltaXphdGlvbmd1aWRlLXBhLmdvb2dsZWFwaXMuY29tAA==", false, 0], "server": "https://optimizationguide-pa.googleapis.com", "supports_spdy": true}, {"anonymization": ["FAAAABAAAABodHRwOi8vbG9jYWxob3N0", false, 0], "server": "https://images.unsplash.com", "supports_spdy": true}, {"alternative_service": [{"advertised_alpns": ["h3"], "expiration": "13400251431224541", "port": 443, "protocol_str": "quic"}], "anonymization": ["FAAAABAAAABodHRwOi8vbG9jYWxob3N0", false, 0], "server": "https://fonts.gstatic.com", "supports_spdy": true}, {"alternative_service": [{"advertised_alpns": ["h3"], "expiration": "13400252169291087", "port": 443, "protocol_str": "quic"}], "anonymization": ["GAAAABIAAABodHRwczovL2dvb2dsZS5jb20AAA==", false, 0], "server": "https://android.clients.google.com", "supports_spdy": true}], "version": 5}, "network_qualities": {"CAISABiAgICA+P////8B": "4G"}}}