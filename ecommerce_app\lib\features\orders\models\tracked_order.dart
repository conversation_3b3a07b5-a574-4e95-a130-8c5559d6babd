import '../../../shared/models/order.dart';
import '../../../shared/models/product.dart';
import 'order_status.dart';
import 'tracking_event.dart';

class TrackedOrder {
  final String id;
  final String orderNumber;
  final DateTime orderDate;
  final OrderStatus currentStatus;
  final double totalAmount;
  final List<OrderItem> items;
  final ShippingAddress shippingAddress;
  final PaymentMethod paymentMethod;
  final List<TrackingEvent> trackingEvents;
  final DateTime? estimatedDelivery;
  final String? trackingNumber;
  final String? courierName;
  final String? courierPhone;

  const TrackedOrder({
    required this.id,
    required this.orderNumber,
    required this.orderDate,
    required this.currentStatus,
    required this.totalAmount,
    required this.items,
    required this.shippingAddress,
    required this.paymentMethod,
    required this.trackingEvents,
    this.estimatedDelivery,
    this.trackingNumber,
    this.courierName,
    this.courierPhone,
  });

  factory TrackedOrder.fromOrder(Order order) {
    return TrackedOrder(
      id: order.id,
      orderNumber: order.orderNumber,
      orderDate: order.orderDate,
      currentStatus: OrderStatus.pending,
      totalAmount: order.totalAmount,
      items: order.items,
      shippingAddress: order.shippingAddress,
      paymentMethod: order.paymentMethod,
      trackingEvents: [],
    );
  }

  factory TrackedOrder.fromJson(Map<String, dynamic> json) {
    return TrackedOrder(
      id: json['id'] as String,
      orderNumber: json['orderNumber'] as String,
      orderDate: DateTime.parse(json['orderDate'] as String),
      currentStatus: OrderStatus.values.firstWhere(
        (e) => e.name == json['currentStatus'],
        orElse: () => OrderStatus.pending,
      ),
      totalAmount: (json['totalAmount'] as num).toDouble(),
      items: (json['items'] as List)
          .map((item) => OrderItem.fromJson(item as Map<String, dynamic>))
          .toList(),
      shippingAddress: ShippingAddress.fromJson(
        json['shippingAddress'] as Map<String, dynamic>,
      ),
      paymentMethod: PaymentMethod.fromJson(
        json['paymentMethod'] as Map<String, dynamic>,
      ),
      trackingEvents: (json['trackingEvents'] as List)
          .map((event) => TrackingEvent.fromJson(event as Map<String, dynamic>))
          .toList(),
      estimatedDelivery: json['estimatedDelivery'] != null
          ? DateTime.parse(json['estimatedDelivery'] as String)
          : null,
      trackingNumber: json['trackingNumber'] as String?,
      courierName: json['courierName'] as String?,
      courierPhone: json['courierPhone'] as String?,
    );
  }

  Map<String, dynamic> toJson() {
    return {
      'id': id,
      'orderNumber': orderNumber,
      'orderDate': orderDate.toIso8601String(),
      'currentStatus': currentStatus.name,
      'totalAmount': totalAmount,
      'items': items.map((item) => item.toJson()).toList(),
      'shippingAddress': shippingAddress.toJson(),
      'paymentMethod': paymentMethod.toJson(),
      'trackingEvents': trackingEvents.map((event) => event.toJson()).toList(),
      'estimatedDelivery': estimatedDelivery?.toIso8601String(),
      'trackingNumber': trackingNumber,
      'courierName': courierName,
      'courierPhone': courierPhone,
    };
  }

  TrackedOrder copyWith({
    String? id,
    String? orderNumber,
    DateTime? orderDate,
    OrderStatus? currentStatus,
    double? totalAmount,
    List<OrderItem>? items,
    ShippingAddress? shippingAddress,
    PaymentMethod? paymentMethod,
    List<TrackingEvent>? trackingEvents,
    DateTime? estimatedDelivery,
    String? trackingNumber,
    String? courierName,
    String? courierPhone,
  }) {
    return TrackedOrder(
      id: id ?? this.id,
      orderNumber: orderNumber ?? this.orderNumber,
      orderDate: orderDate ?? this.orderDate,
      currentStatus: currentStatus ?? this.currentStatus,
      totalAmount: totalAmount ?? this.totalAmount,
      items: items ?? this.items,
      shippingAddress: shippingAddress ?? this.shippingAddress,
      paymentMethod: paymentMethod ?? this.paymentMethod,
      trackingEvents: trackingEvents ?? this.trackingEvents,
      estimatedDelivery: estimatedDelivery ?? this.estimatedDelivery,
      trackingNumber: trackingNumber ?? this.trackingNumber,
      courierName: courierName ?? this.courierName,
      courierPhone: courierPhone ?? this.courierPhone,
    );
  }

  @override
  bool operator ==(Object other) {
    if (identical(this, other)) return true;
    return other is TrackedOrder && other.id == id;
  }

  @override
  int get hashCode => id.hashCode;

  @override
  String toString() {
    return 'TrackedOrder(id: $id, orderNumber: $orderNumber, currentStatus: $currentStatus)';
  }
}
